<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./drivers/encoder.o ./drivers/flash.o ./drivers/imu.o ./drivers/key.o ./drivers/led.o ./drivers/motor.o ./drivers/nrf24l01.o ./drivers/oled.o ./drivers/oled_font.o ./drivers/openmv.o ./drivers/timer.o ./soft/debug.o ./soft/delay.o ./soft/menu.o ./soft/pid.o ./soft/protocol.o ./soft/task.o ./soft/vofa.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6889fa08</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1b35</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>flash.o</file>
         <name>flash.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>imu.o</file>
         <name>imu.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>led.o</file>
         <name>led.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>nrf24l01.o</file>
         <name>nrf24l01.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>oled_font.o</file>
         <name>oled_font.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>openmv.o</file>
         <name>openmv.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>timer.o</file>
         <name>timer.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>debug.o</file>
         <name>debug.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>menu.o</file>
         <name>menu.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>protocol.o</file>
         <name>protocol.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>task.o</file>
         <name>task.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>vofa.o</file>
         <name>vofa.o</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-26">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>vsnprintf.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-58">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-59">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-5a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-5b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-5c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-5d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-127">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-128">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-129">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.imu_analysis</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x214</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text.encoder_exti_callback</name>
         <load_address>0x2d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d4</run_address>
         <size>0x20c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x4e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e0</run_address>
         <size>0x1dc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x6bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6bc</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0x7c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c0</run_address>
         <size>0xf0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x8b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8b0</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.incremental_pid</name>
         <load_address>0x998</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x998</run_address>
         <size>0xe4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text</name>
         <load_address>0xa7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa7c</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.UART1_IRQHandler</name>
         <load_address>0xb54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb54</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xc1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc1c</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.nrf24l01_spi_read_write</name>
         <load_address>0xce0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce0</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-75">
         <name>.text.nrf24l01_receive_callback</name>
         <load_address>0xd88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd88</run_address>
         <size>0xa4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.left_motor_set_duty</name>
         <load_address>0xe2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe2c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.right_motor_set_duty</name>
         <load_address>0xebc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xebc</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.SYSCFG_DL_PWM_6_init</name>
         <load_address>0xf4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf4c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.SYSCFG_DL_PWM_7_init</name>
         <load_address>0xfd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd8</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.__mulsf3</name>
         <load_address>0x1064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1064</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.timerA_callback</name>
         <load_address>0x10f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f0</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x1178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1178</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.__divsf3</name>
         <load_address>0x11fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11fc</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.timerB_callback</name>
         <load_address>0x127e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x127e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1280</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1280</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.nrf24l01_read_buf</name>
         <load_address>0x12fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12fc</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1360</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1360</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x13c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13c4</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.protocol_analysis</name>
         <load_address>0x1420</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1420</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x147c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x147c</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x14d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14d0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.nrf24l01_write_reg</name>
         <load_address>0x151c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x151c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.text.DL_UART_init</name>
         <load_address>0x1568</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1568</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x15b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.SYSCFG_DL_UART_3_init</name>
         <load_address>0x15f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15f8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1640</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1640</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.encoder_callback</name>
         <load_address>0x1684</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1684</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.nrf24l01_read_reg</name>
         <load_address>0x16c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16c8</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.SYSCFG_DL_SPI_1_init</name>
         <load_address>0x170c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x170c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.SYSCFG_DL_TIMER_8_init</name>
         <load_address>0x174c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x174c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-192">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x178c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x178c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.SYSCFG_DL_TIMER_12_init</name>
         <load_address>0x17c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.__floatsisf</name>
         <load_address>0x1804</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1804</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.__gtsf2</name>
         <load_address>0x1840</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1840</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text.get_verify_code</name>
         <load_address>0x187c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x187c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x18b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-138">
         <name>.text.__eqsf2</name>
         <load_address>0x18f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18f4</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.__muldsi3</name>
         <load_address>0x1930</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1930</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.__fixsfsi</name>
         <load_address>0x196c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x196c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x19a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19a4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.get_verify_code</name>
         <load_address>0x19d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19d8</run_address>
         <size>0x32</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1a0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a0c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1a3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a3c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1a68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a68</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1a94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a94</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x1abc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1abc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x1ae4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.motor_set_duty</name>
         <load_address>0x1b0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b0c</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1b34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b34</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x1b5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b5c</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1b80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b80</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1ba4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ba4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.main</name>
         <load_address>0x1bc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bc4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.openmv_analysis</name>
         <load_address>0x1be4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1be4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1c04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c04</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1c24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c24</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1c40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c40</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1c5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c5c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x1c78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c78</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1c94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c94</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x1cb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cb0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1ccc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ccc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x1ce8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ce8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1d04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d04</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-190">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1d20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d20</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1d3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d3c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text.TIMG12_IRQHandler</name>
         <load_address>0x1d58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d58</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG8_IRQHandler</name>
         <load_address>0x1d74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d74</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1d90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d90</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1da8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1da8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1dc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dc0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1dd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dd8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1df0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1df0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1e08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e08</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1e20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x1e38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e38</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x1e50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x1e68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x1e80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1e98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1eb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eb0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-177">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1ec8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ec8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-193">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1ee0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ee0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1ef8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ef8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1f10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f10</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1f28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f28</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1f3e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f3e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1f54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f54</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.debug_init</name>
         <load_address>0x1f6a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f6a</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x1f80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f80</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1f96</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f96</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1faa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1faa</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x1fc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fc0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1fd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fd4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-196">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1fe8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fe8</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x1ffc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ffc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2010</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2024</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2024</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x2038</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2038</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x204a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x204a</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x205c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x205c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x206e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x206e</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x2080</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2080</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2092</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2092</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x20a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a4</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x20b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b6</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x20c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x20d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20d8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x20e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20e8</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x20f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f8</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2104</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2104</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.text.nrf24l01_spi_delay</name>
         <load_address>0x210e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x210e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x2118</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2118</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text:abort</name>
         <load_address>0x2120</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2120</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x2126</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2126</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.HOSTexit</name>
         <load_address>0x212a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x212a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x212e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x212e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text._system_pre_init</name>
         <load_address>0x2132</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2132</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-214">
         <name>__TI_handler_table</name>
         <load_address>0x21b0</load_address>
         <readonly>true</readonly>
         <run_address>0x21b0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-217">
         <name>.cinit..bss.load</name>
         <load_address>0x21bc</load_address>
         <readonly>true</readonly>
         <run_address>0x21bc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-216">
         <name>.cinit..data.load</name>
         <load_address>0x21c4</load_address>
         <readonly>true</readonly>
         <run_address>0x21c4</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-215">
         <name>__TI_cinit_table</name>
         <load_address>0x21cc</load_address>
         <readonly>true</readonly>
         <run_address>0x21cc</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a0">
         <name>.rodata.gTIMER_12TimerConfig</name>
         <load_address>0x2138</load_address>
         <readonly>true</readonly>
         <run_address>0x2138</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.rodata.gTIMER_8TimerConfig</name>
         <load_address>0x214c</load_address>
         <readonly>true</readonly>
         <run_address>0x214c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.rodata.gSPI_1_config</name>
         <load_address>0x2160</load_address>
         <readonly>true</readonly>
         <run_address>0x2160</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x216a</load_address>
         <readonly>true</readonly>
         <run_address>0x216a</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x2174</load_address>
         <readonly>true</readonly>
         <run_address>0x2174</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.rodata.gUART_3Config</name>
         <load_address>0x217e</load_address>
         <readonly>true</readonly>
         <run_address>0x217e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-198">
         <name>.rodata.gPWM_6Config</name>
         <load_address>0x2188</load_address>
         <readonly>true</readonly>
         <run_address>0x2188</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.rodata.gPWM_7Config</name>
         <load_address>0x2190</load_address>
         <readonly>true</readonly>
         <run_address>0x2190</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-197">
         <name>.rodata.gPWM_6ClockConfig</name>
         <load_address>0x2198</load_address>
         <readonly>true</readonly>
         <run_address>0x2198</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-199">
         <name>.rodata.gPWM_7ClockConfig</name>
         <load_address>0x219b</load_address>
         <readonly>true</readonly>
         <run_address>0x219b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.rodata.gTIMER_12ClockConfig</name>
         <load_address>0x219e</load_address>
         <readonly>true</readonly>
         <run_address>0x219e</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.rodata.gTIMER_8ClockConfig</name>
         <load_address>0x21a1</load_address>
         <readonly>true</readonly>
         <run_address>0x21a1</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.rodata.gSPI_1_clockConfig</name>
         <load_address>0x21a4</load_address>
         <readonly>true</readonly>
         <run_address>0x21a4</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x21a6</load_address>
         <readonly>true</readonly>
         <run_address>0x21a6</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x21a8</load_address>
         <readonly>true</readonly>
         <run_address>0x21a8</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.rodata.gUART_3ClockConfig</name>
         <load_address>0x21aa</load_address>
         <readonly>true</readonly>
         <run_address>0x21aa</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b6">
         <name>.data.left_counter</name>
         <load_address>0x2020026c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020026c</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.data.right_counter</name>
         <load_address>0x20200270</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200270</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.data.left_speed</name>
         <load_address>0x2020026e</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020026e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.data.left_distance</name>
         <load_address>0x20200264</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200264</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-df">
         <name>.data.right_speed</name>
         <load_address>0x20200272</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200272</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-129">
         <name>.data.right_distance</name>
         <load_address>0x20200268</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200268</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.data.k</name>
         <load_address>0x20200260</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200260</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.data.imu_analysis.last_angle_z</name>
         <load_address>0x2020025c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020025c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.data.imu_analysis.angle_z</name>
         <load_address>0x20200258</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200258</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.data.state</name>
         <load_address>0x20200278</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200278</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-90">
         <name>.data.n</name>
         <load_address>0x20200275</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200275</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.data.receive_flag</name>
         <load_address>0x20200277</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200277</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.data.irq</name>
         <load_address>0x20200274</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200274</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-85">
         <name>.data.state</name>
         <load_address>0x20200279</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200279</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-86">
         <name>.data.n</name>
         <load_address>0x20200276</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200276</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-96">
         <name>.data.uart_data</name>
         <load_address>0x2020027a</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020027a</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-91">
         <name>.bss.buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200234</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-87">
         <name>.bss.rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020023f</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.bss.data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024b</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.common:target_speed</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200254</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.common:left_speed_pid</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200170</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e0">
         <name>.common:right_speed_pid</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020019c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-153">
         <name>.common:gPWM_6Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-154">
         <name>.common:gPWM_7Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000a0</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-155">
         <name>.common:gUART_3Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-156">
         <name>.common:gSPI_1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f2">
         <name>.common:angle</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020021c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f4">
         <name>.common:gyroscope</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200228</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f5">
         <name>.common:acceleration</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200210</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-8e">
         <name>.common:data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200256</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-c5">
         <name>.common:receive_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-e1">
         <name>.common:openmvData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200247</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-122">
         <name>.common:adcValue1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024c</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-123">
         <name>.common:medAdcValue1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200250</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-124">
         <name>.common:adcValue2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-125">
         <name>.common:medAdcValue2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200252</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-219">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_abbrev</name>
         <load_address>0xd2</load_address>
         <run_address>0xd2</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_abbrev</name>
         <load_address>0x2e2</load_address>
         <run_address>0x2e2</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x34f</load_address>
         <run_address>0x34f</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_abbrev</name>
         <load_address>0x4d4</load_address>
         <run_address>0x4d4</run_address>
         <size>0x1b3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0x687</load_address>
         <run_address>0x687</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0x77f</load_address>
         <run_address>0x77f</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x934</load_address>
         <run_address>0x934</run_address>
         <size>0x1da</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0xb0e</load_address>
         <run_address>0xb0e</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_abbrev</name>
         <load_address>0xc73</load_address>
         <run_address>0xc73</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_abbrev</name>
         <load_address>0xe37</load_address>
         <run_address>0xe37</run_address>
         <size>0x9d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_abbrev</name>
         <load_address>0xed4</load_address>
         <run_address>0xed4</run_address>
         <size>0x53</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_abbrev</name>
         <load_address>0xf27</load_address>
         <run_address>0xf27</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.debug_abbrev</name>
         <load_address>0xf89</load_address>
         <run_address>0xf89</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_abbrev</name>
         <load_address>0x1200</load_address>
         <run_address>0x1200</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_abbrev</name>
         <load_address>0x1486</load_address>
         <run_address>0x1486</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x1721</load_address>
         <run_address>0x1721</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_abbrev</name>
         <load_address>0x17d0</load_address>
         <run_address>0x17d0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x1940</load_address>
         <run_address>0x1940</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_abbrev</name>
         <load_address>0x1979</load_address>
         <run_address>0x1979</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x1a3b</load_address>
         <run_address>0x1a3b</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_abbrev</name>
         <load_address>0x1aab</load_address>
         <run_address>0x1aab</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_abbrev</name>
         <load_address>0x1b38</load_address>
         <run_address>0x1b38</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.debug_abbrev</name>
         <load_address>0x1bd0</load_address>
         <run_address>0x1bd0</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_abbrev</name>
         <load_address>0x1bfc</load_address>
         <run_address>0x1bfc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_abbrev</name>
         <load_address>0x1c23</load_address>
         <run_address>0x1c23</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_abbrev</name>
         <load_address>0x1c4a</load_address>
         <run_address>0x1c4a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0x1c71</load_address>
         <run_address>0x1c71</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x1c98</load_address>
         <run_address>0x1c98</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_abbrev</name>
         <load_address>0x1cbf</load_address>
         <run_address>0x1cbf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_abbrev</name>
         <load_address>0x1ce6</load_address>
         <run_address>0x1ce6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_abbrev</name>
         <load_address>0x1d0d</load_address>
         <run_address>0x1d0d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_abbrev</name>
         <load_address>0x1d34</load_address>
         <run_address>0x1d34</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_abbrev</name>
         <load_address>0x1d59</load_address>
         <run_address>0x1d59</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-21b">
         <name>.debug_abbrev</name>
         <load_address>0x1d7e</load_address>
         <run_address>0x1d7e</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_info</name>
         <load_address>0x1e7</load_address>
         <run_address>0x1e7</run_address>
         <size>0x39d1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3bb8</load_address>
         <run_address>0x3bb8</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_info</name>
         <load_address>0x3c38</load_address>
         <run_address>0x3c38</run_address>
         <size>0xb04</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0x473c</load_address>
         <run_address>0x473c</run_address>
         <size>0x9d4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0x5110</load_address>
         <run_address>0x5110</run_address>
         <size>0x7f3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_info</name>
         <load_address>0x5903</load_address>
         <run_address>0x5903</run_address>
         <size>0xeeb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_info</name>
         <load_address>0x67ee</load_address>
         <run_address>0x67ee</run_address>
         <size>0xa45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_info</name>
         <load_address>0x7233</load_address>
         <run_address>0x7233</run_address>
         <size>0xa13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_info</name>
         <load_address>0x7c46</load_address>
         <run_address>0x7c46</run_address>
         <size>0xa81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0x86c7</load_address>
         <run_address>0x86c7</run_address>
         <size>0x2b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0x8977</load_address>
         <run_address>0x8977</run_address>
         <size>0x9f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_info</name>
         <load_address>0x8a16</load_address>
         <run_address>0x8a16</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_info</name>
         <load_address>0x8a8b</load_address>
         <run_address>0x8a8b</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_info</name>
         <load_address>0x9bcd</load_address>
         <run_address>0x9bcd</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.debug_info</name>
         <load_address>0xcd3f</load_address>
         <run_address>0xcd3f</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xdfe5</load_address>
         <run_address>0xdfe5</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0xe408</load_address>
         <run_address>0xe408</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_info</name>
         <load_address>0xeb4c</load_address>
         <run_address>0xeb4c</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0xeb92</load_address>
         <run_address>0xeb92</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xed24</load_address>
         <run_address>0xed24</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xedea</load_address>
         <run_address>0xedea</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_info</name>
         <load_address>0xef66</load_address>
         <run_address>0xef66</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_info</name>
         <load_address>0xf05e</load_address>
         <run_address>0xf05e</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_info</name>
         <load_address>0xf099</load_address>
         <run_address>0xf099</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_info</name>
         <load_address>0xf240</load_address>
         <run_address>0xf240</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_info</name>
         <load_address>0xf3cf</load_address>
         <run_address>0xf3cf</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_info</name>
         <load_address>0xf55c</load_address>
         <run_address>0xf55c</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_info</name>
         <load_address>0xf6e9</load_address>
         <run_address>0xf6e9</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_info</name>
         <load_address>0xf878</load_address>
         <run_address>0xf878</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_info</name>
         <load_address>0xfa0b</load_address>
         <run_address>0xfa0b</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_info</name>
         <load_address>0xfc22</load_address>
         <run_address>0xfc22</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_info</name>
         <load_address>0xfdbb</load_address>
         <run_address>0xfdbb</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_info</name>
         <load_address>0xff7c</load_address>
         <run_address>0xff7c</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-21a">
         <name>.debug_info</name>
         <load_address>0x10276</load_address>
         <run_address>0x10276</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_ranges</name>
         <load_address>0x28</load_address>
         <run_address>0x28</run_address>
         <size>0x1a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_ranges</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_ranges</name>
         <load_address>0x220</load_address>
         <run_address>0x220</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_ranges</name>
         <load_address>0x268</load_address>
         <run_address>0x268</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_ranges</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_ranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0x450</load_address>
         <run_address>0x450</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_ranges</name>
         <load_address>0x808</load_address>
         <run_address>0x808</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.debug_ranges</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_ranges</name>
         <load_address>0xb88</load_address>
         <run_address>0xb88</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_ranges</name>
         <load_address>0xbd0</load_address>
         <run_address>0xbd0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_ranges</name>
         <load_address>0xc18</load_address>
         <run_address>0xc18</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_ranges</name>
         <load_address>0xc30</load_address>
         <run_address>0xc30</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_ranges</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_ranges</name>
         <load_address>0xc98</load_address>
         <run_address>0xc98</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_ranges</name>
         <load_address>0xcd0</load_address>
         <run_address>0xcd0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x247</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_str</name>
         <load_address>0x247</load_address>
         <run_address>0x247</run_address>
         <size>0x300a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_str</name>
         <load_address>0x3251</load_address>
         <run_address>0x3251</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_str</name>
         <load_address>0x33c4</load_address>
         <run_address>0x33c4</run_address>
         <size>0x7de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_str</name>
         <load_address>0x3ba2</load_address>
         <run_address>0x3ba2</run_address>
         <size>0x871</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_str</name>
         <load_address>0x4413</load_address>
         <run_address>0x4413</run_address>
         <size>0x4ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x48cd</load_address>
         <run_address>0x48cd</run_address>
         <size>0x6ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0x4f87</load_address>
         <run_address>0x4f87</run_address>
         <size>0x8a4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x582b</load_address>
         <run_address>0x582b</run_address>
         <size>0x87c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_str</name>
         <load_address>0x60a7</load_address>
         <run_address>0x60a7</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_str</name>
         <load_address>0x6956</load_address>
         <run_address>0x6956</run_address>
         <size>0x1f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_str</name>
         <load_address>0x6b4f</load_address>
         <run_address>0x6b4f</run_address>
         <size>0x137</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_str</name>
         <load_address>0x6c86</load_address>
         <run_address>0x6c86</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.debug_str</name>
         <load_address>0x6dfe</load_address>
         <run_address>0x6dfe</run_address>
         <size>0xc46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_str</name>
         <load_address>0x7a44</load_address>
         <run_address>0x7a44</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_str</name>
         <load_address>0x981b</load_address>
         <run_address>0x981b</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_str</name>
         <load_address>0xa509</load_address>
         <run_address>0xa509</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_str</name>
         <load_address>0xa72e</load_address>
         <run_address>0xa72e</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0xaa5d</load_address>
         <run_address>0xaa5d</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_str</name>
         <load_address>0xab52</load_address>
         <run_address>0xab52</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0xaced</load_address>
         <run_address>0xaced</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_str</name>
         <load_address>0xae55</load_address>
         <run_address>0xae55</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_str</name>
         <load_address>0xb02a</load_address>
         <run_address>0xb02a</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_str</name>
         <load_address>0xb172</load_address>
         <run_address>0xb172</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_frame</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x4b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x528</load_address>
         <run_address>0x528</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_frame</name>
         <load_address>0x558</load_address>
         <run_address>0x558</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_frame</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_frame</name>
         <load_address>0x6c8</load_address>
         <run_address>0x6c8</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_frame</name>
         <load_address>0x764</load_address>
         <run_address>0x764</run_address>
         <size>0x208</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_frame</name>
         <load_address>0x96c</load_address>
         <run_address>0x96c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_frame</name>
         <load_address>0xa6c</load_address>
         <run_address>0xa6c</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_frame</name>
         <load_address>0xb20</load_address>
         <run_address>0xb20</run_address>
         <size>0x140</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_frame</name>
         <load_address>0xc60</load_address>
         <run_address>0xc60</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_frame</name>
         <load_address>0xce0</load_address>
         <run_address>0xce0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_frame</name>
         <load_address>0xd04</load_address>
         <run_address>0xd04</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_frame</name>
         <load_address>0xd24</load_address>
         <run_address>0xd24</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_frame</name>
         <load_address>0xf58</load_address>
         <run_address>0xf58</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.debug_frame</name>
         <load_address>0x1360</load_address>
         <run_address>0x1360</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_frame</name>
         <load_address>0x1518</load_address>
         <run_address>0x1518</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_frame</name>
         <load_address>0x15a8</load_address>
         <run_address>0x15a8</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_frame</name>
         <load_address>0x16a8</load_address>
         <run_address>0x16a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x16c8</load_address>
         <run_address>0x16c8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x1700</load_address>
         <run_address>0x1700</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x1728</load_address>
         <run_address>0x1728</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_frame</name>
         <load_address>0x1758</load_address>
         <run_address>0x1758</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_frame</name>
         <load_address>0x1788</load_address>
         <run_address>0x1788</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_line</name>
         <load_address>0x1d0</load_address>
         <run_address>0x1d0</run_address>
         <size>0xd15</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xee5</load_address>
         <run_address>0xee5</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_line</name>
         <load_address>0xf9d</load_address>
         <run_address>0xf9d</run_address>
         <size>0x48b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x1428</load_address>
         <run_address>0x1428</run_address>
         <size>0x551</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_line</name>
         <load_address>0x1979</load_address>
         <run_address>0x1979</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_line</name>
         <load_address>0x1c72</load_address>
         <run_address>0x1c72</run_address>
         <size>0x8e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_line</name>
         <load_address>0x2559</load_address>
         <run_address>0x2559</run_address>
         <size>0x4db</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_line</name>
         <load_address>0x2a34</load_address>
         <run_address>0x2a34</run_address>
         <size>0x318</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x2d4c</load_address>
         <run_address>0x2d4c</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x31df</load_address>
         <run_address>0x31df</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_line</name>
         <load_address>0x347a</load_address>
         <run_address>0x347a</run_address>
         <size>0x145</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_line</name>
         <load_address>0x35bf</load_address>
         <run_address>0x35bf</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.debug_line</name>
         <load_address>0x3738</load_address>
         <run_address>0x3738</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_line</name>
         <load_address>0x4353</load_address>
         <run_address>0x4353</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.debug_line</name>
         <load_address>0x5ac2</load_address>
         <run_address>0x5ac2</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_line</name>
         <load_address>0x64da</load_address>
         <run_address>0x64da</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_line</name>
         <load_address>0x66b6</load_address>
         <run_address>0x66b6</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_line</name>
         <load_address>0x6bd0</load_address>
         <run_address>0x6bd0</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x6c0e</load_address>
         <run_address>0x6c0e</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x6d0c</load_address>
         <run_address>0x6d0c</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x6dcc</load_address>
         <run_address>0x6dcc</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_line</name>
         <load_address>0x6f94</load_address>
         <run_address>0x6f94</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_line</name>
         <load_address>0x6ffb</load_address>
         <run_address>0x6ffb</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_line</name>
         <load_address>0x703c</load_address>
         <run_address>0x703c</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_line</name>
         <load_address>0x7143</load_address>
         <run_address>0x7143</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_line</name>
         <load_address>0x71fc</load_address>
         <run_address>0x71fc</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_line</name>
         <load_address>0x72dc</load_address>
         <run_address>0x72dc</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_line</name>
         <load_address>0x73b8</load_address>
         <run_address>0x73b8</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x7470</load_address>
         <run_address>0x7470</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_line</name>
         <load_address>0x752c</load_address>
         <run_address>0x752c</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_line</name>
         <load_address>0x75f3</load_address>
         <run_address>0x75f3</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_line</name>
         <load_address>0x7697</load_address>
         <run_address>0x7697</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_line</name>
         <load_address>0x779b</load_address>
         <run_address>0x779b</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_loc</name>
         <load_address>0x2250</load_address>
         <run_address>0x2250</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_loc</name>
         <load_address>0x2a0c</load_address>
         <run_address>0x2a0c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_loc</name>
         <load_address>0x2ae4</load_address>
         <run_address>0x2ae4</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_loc</name>
         <load_address>0x2f08</load_address>
         <run_address>0x2f08</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_loc</name>
         <load_address>0x3074</load_address>
         <run_address>0x3074</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_loc</name>
         <load_address>0x30e3</load_address>
         <run_address>0x30e3</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_loc</name>
         <load_address>0x324a</load_address>
         <run_address>0x324a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_aranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2078</size>
         <contents>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x21b0</load_address>
         <run_address>0x21b0</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-214"/>
            <object_component_ref idref="oc-217"/>
            <object_component_ref idref="oc-216"/>
            <object_component_ref idref="oc-215"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2138</load_address>
         <run_address>0x2138</run_address>
         <size>0x78</size>
         <contents>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-1b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1de"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200258</run_address>
         <size>0x23</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x257</size>
         <contents>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-125"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-219"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d5" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d6" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d7" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d8" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d9" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1da" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1dc" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1f8" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d8d</size>
         <contents>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-21b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1fa" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10332</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-21a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1fc" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf8</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-fb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1fe" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb25b</size>
         <contents>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-1c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-200" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17a8</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-15c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-202" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x783b</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-f9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-204" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3270</size>
         <contents>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-10e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20e" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x158</size>
         <contents>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-fa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-218" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-226" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21e0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-227" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x27b</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-228" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x21e0</used_space>
         <unused_space>0x5e20</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2078</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2138</start_address>
               <size>0x78</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x21b0</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x21e0</start_address>
               <size>0x5e20</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x47a</used_space>
         <unused_space>0x3b86</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1da"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1dc"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x257</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200257</start_address>
               <size>0x1</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200258</start_address>
               <size>0x23</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020027b</start_address>
               <size>0x3b85</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x21bc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x257</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x21c4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200258</run_address>
            <run_size>0x23</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x21cc</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x21dc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x21dc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x21b0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x21bc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x1bc5</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-44">
         <name>timerA_callback</name>
         <value>0x10f1</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-45">
         <name>target_speed</name>
         <value>0x20200254</value>
      </symbol>
      <symbol id="sm-46">
         <name>left_speed_pid</name>
         <value>0x20200170</value>
      </symbol>
      <symbol id="sm-47">
         <name>right_speed_pid</name>
         <value>0x2020019c</value>
      </symbol>
      <symbol id="sm-48">
         <name>timerB_callback</name>
         <value>0x127f</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-49">
         <name>GROUP1_IRQHandler</name>
         <value>0x20f9</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-11f">
         <name>SYSCFG_DL_init</name>
         <value>0x13c5</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-120">
         <name>SYSCFG_DL_initPower</name>
         <value>0xc1d</value>
         <object_component_ref idref="oc-147"/>
      </symbol>
      <symbol id="sm-121">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x4e1</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-122">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1b81</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-123">
         <name>SYSCFG_DL_PWM_6_init</name>
         <value>0xf4d</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-124">
         <name>SYSCFG_DL_PWM_7_init</name>
         <value>0xfd9</value>
         <object_component_ref idref="oc-14b"/>
      </symbol>
      <symbol id="sm-125">
         <name>SYSCFG_DL_TIMER_8_init</name>
         <value>0x174d</value>
         <object_component_ref idref="oc-14c"/>
      </symbol>
      <symbol id="sm-126">
         <name>SYSCFG_DL_TIMER_12_init</name>
         <value>0x17c9</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-127">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x15b1</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-128">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x147d</value>
         <object_component_ref idref="oc-14f"/>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_UART_3_init</name>
         <value>0x15f9</value>
         <object_component_ref idref="oc-150"/>
      </symbol>
      <symbol id="sm-12a">
         <name>SYSCFG_DL_SPI_1_init</name>
         <value>0x170d</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x20e9</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-12c">
         <name>gPWM_6Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-12d">
         <name>gPWM_7Backup</name>
         <value>0x202000a0</value>
      </symbol>
      <symbol id="sm-12e">
         <name>gUART_3Backup</name>
         <value>0x20200140</value>
      </symbol>
      <symbol id="sm-12f">
         <name>gSPI_1Backup</name>
         <value>0x202001c8</value>
      </symbol>
      <symbol id="sm-13a">
         <name>Default_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13b">
         <name>Reset_Handler</name>
         <value>0x212f</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-13c">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-13d">
         <name>NMI_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>HardFault_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>SVC_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>PendSV_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>SysTick_Handler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>GROUP0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>ADC0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>ADC1_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>CANFD0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-146">
         <name>DAC0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-147">
         <name>SPI0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>SPI1_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>UART2_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>TIMG0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>TIMG6_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>TIMA0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>TIMA1_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>TIMG7_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>I2C0_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>I2C1_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-151">
         <name>AES_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-152">
         <name>RTC_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-153">
         <name>DMA_IRQHandler</name>
         <value>0x2127</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16b">
         <name>encoder_callback</name>
         <value>0x1685</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-16c">
         <name>left_counter</name>
         <value>0x2020026c</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-16d">
         <name>left_speed</name>
         <value>0x2020026e</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-16e">
         <name>left_distance</name>
         <value>0x20200264</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-16f">
         <name>right_counter</name>
         <value>0x20200270</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-170">
         <name>right_speed</name>
         <value>0x20200272</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-171">
         <name>right_distance</name>
         <value>0x20200268</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-172">
         <name>encoder_exti_callback</name>
         <value>0x2d5</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-195">
         <name>imu_analysis</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-196">
         <name>angle</name>
         <value>0x2020021c</value>
      </symbol>
      <symbol id="sm-197">
         <name>gyroscope</name>
         <value>0x20200228</value>
      </symbol>
      <symbol id="sm-198">
         <name>acceleration</name>
         <value>0x20200210</value>
      </symbol>
      <symbol id="sm-199">
         <name>k</name>
         <value>0x20200260</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-19a">
         <name>UART1_IRQHandler</name>
         <value>0xb55</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-19b">
         <name>data</name>
         <value>0x20200256</value>
      </symbol>
      <symbol id="sm-1ac">
         <name>left_motor_set_duty</name>
         <value>0xe2d</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1ad">
         <name>right_motor_set_duty</name>
         <value>0xebd</value>
         <object_component_ref idref="oc-12f"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>motor_set_duty</name>
         <value>0x1b0d</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-1dc">
         <name>nrf24l01_spi_read_write</name>
         <value>0xce1</value>
         <object_component_ref idref="oc-121"/>
      </symbol>
      <symbol id="sm-1dd">
         <name>nrf24l01_receive_callback</name>
         <value>0xd89</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-1de">
         <name>irq</name>
         <value>0x20200274</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-1df">
         <name>receive_buffer</name>
         <value>0x202001f0</value>
      </symbol>
      <symbol id="sm-1e0">
         <name>receive_flag</name>
         <value>0x20200277</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-201">
         <name>openmv_analysis</name>
         <value>0x1be5</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-202">
         <name>openmvData</name>
         <value>0x20200247</value>
      </symbol>
      <symbol id="sm-203">
         <name>UART3_IRQHandler</name>
         <value>0x7c1</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-213">
         <name>TIMG8_IRQHandler</name>
         <value>0x1d75</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-214">
         <name>TIMG12_IRQHandler</name>
         <value>0x1d59</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-22f">
         <name>debug_init</name>
         <value>0x1f6b</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-230">
         <name>UART0_IRQHandler</name>
         <value>0x1ae5</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-231">
         <name>uart_data</name>
         <value>0x2020027a</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-23c">
         <name>incremental_pid</name>
         <value>0x999</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-245">
         <name>protocol_analysis</name>
         <value>0x1421</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-246">
         <name>adcValue1</name>
         <value>0x2020024c</value>
      </symbol>
      <symbol id="sm-247">
         <name>medAdcValue1</name>
         <value>0x20200250</value>
      </symbol>
      <symbol id="sm-248">
         <name>adcValue2</name>
         <value>0x2020024e</value>
      </symbol>
      <symbol id="sm-249">
         <name>medAdcValue2</name>
         <value>0x20200252</value>
      </symbol>
      <symbol id="sm-24c">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24d">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24e">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24f">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-250">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-251">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-252">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-253">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-254">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-25d">
         <name>DL_Common_delayCycles</name>
         <value>0x2105</value>
         <object_component_ref idref="oc-17e"/>
      </symbol>
      <symbol id="sm-26a">
         <name>DL_SPI_init</name>
         <value>0x1641</value>
         <object_component_ref idref="oc-1b8"/>
      </symbol>
      <symbol id="sm-26b">
         <name>DL_SPI_setClockConfig</name>
         <value>0x2039</value>
         <object_component_ref idref="oc-1b2"/>
      </symbol>
      <symbol id="sm-287">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1d21</value>
         <object_component_ref idref="oc-190"/>
      </symbol>
      <symbol id="sm-288">
         <name>DL_Timer_initTimerMode</name>
         <value>0x8b1</value>
         <object_component_ref idref="oc-19b"/>
      </symbol>
      <symbol id="sm-289">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x20d9</value>
         <object_component_ref idref="oc-16d"/>
      </symbol>
      <symbol id="sm-28a">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1d05</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-28b">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1ee1</value>
         <object_component_ref idref="oc-193"/>
      </symbol>
      <symbol id="sm-28c">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x6bd</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-299">
         <name>DL_UART_init</name>
         <value>0x1569</value>
         <object_component_ref idref="oc-1a7"/>
      </symbol>
      <symbol id="sm-29a">
         <name>DL_UART_setClockConfig</name>
         <value>0x2093</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-2a7">
         <name>_c_int00_noargs</name>
         <value>0x1b35</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-2a8">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2b4">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x18b9</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-2bc">
         <name>_system_pre_init</name>
         <value>0x2133</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-2c7">
         <name>__TI_zero_init_nomemset</name>
         <value>0x1f81</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-2d0">
         <name>__TI_decompress_none</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2db">
         <name>__TI_decompress_lzss</name>
         <value>0x1281</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2ea">
         <name>abort</name>
         <value>0x2121</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-302">
         <name>HOSTexit</name>
         <value>0x212b</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-303">
         <name>C$$EXIT</name>
         <value>0x212a</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-318">
         <name>__aeabi_fadd</name>
         <value>0xa87</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-319">
         <name>__addsf3</name>
         <value>0xa87</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-31a">
         <name>__aeabi_fsub</name>
         <value>0xa7d</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-31b">
         <name>__subsf3</name>
         <value>0xa7d</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-321">
         <name>__muldsi3</name>
         <value>0x1931</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-327">
         <name>__aeabi_fmul</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-328">
         <name>__mulsf3</name>
         <value>0x1065</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-32e">
         <name>__aeabi_fdiv</name>
         <value>0x11fd</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-32f">
         <name>__divsf3</name>
         <value>0x11fd</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-335">
         <name>__aeabi_f2iz</name>
         <value>0x196d</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-336">
         <name>__fixsfsi</name>
         <value>0x196d</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-33c">
         <name>__aeabi_i2f</name>
         <value>0x1805</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-33d">
         <name>__floatsisf</name>
         <value>0x1805</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-343">
         <name>__aeabi_fcmpeq</name>
         <value>0x1361</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-344">
         <name>__aeabi_fcmplt</name>
         <value>0x1375</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-345">
         <name>__aeabi_fcmple</name>
         <value>0x1389</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-346">
         <name>__aeabi_fcmpge</name>
         <value>0x139d</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-347">
         <name>__aeabi_fcmpgt</name>
         <value>0x13b1</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-34d">
         <name>__aeabi_memcpy</name>
         <value>0x2119</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-34e">
         <name>__aeabi_memcpy4</name>
         <value>0x2119</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-34f">
         <name>__aeabi_memcpy8</name>
         <value>0x2119</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-358">
         <name>__eqsf2</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-359">
         <name>__lesf2</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-35a">
         <name>__ltsf2</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-35b">
         <name>__nesf2</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-35c">
         <name>__cmpsf2</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-35d">
         <name>__gtsf2</name>
         <value>0x1841</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-35e">
         <name>__gesf2</name>
         <value>0x1841</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-36a">
         <name>TI_memcpy_small</name>
         <value>0x20a5</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-36b">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-36e">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-36f">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
