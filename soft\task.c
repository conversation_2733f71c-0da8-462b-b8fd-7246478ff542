//
// Created by faz<PERSON><PERSON> on 2024/7/24.
//

#include "task.h"

uint8_t show_attitude(uint8_t keyNum)
{
    oled_clear_buffer();
    oled_show_string(0, 0, "pitch:");
    oled_show_string(0, 10, "roll :");
    oled_show_string(0, 20, "yaw  :");
    oled_show_float(36, 0, angle.x, 3);
    oled_show_float(36, 12, angle.y, 3);
    oled_show_float(36, 24, angle.z, 3);
    oled_update_screen();
    vofa_add_data(angle.x);
    vofa_add_data(angle.y);
    vofa_add_data(angle.z);
    vofa_send();
    if (keyNum == BACK){
        return BACK;
    }
    return NO_SHOW;
}

uint8_t remote_control(uint8_t keyNum)
{
    int16_t duty;
    int16_t temp;
    oled_clear_buffer();
    while (1){
        oled_show_string(0, 0, "remote control");
        if (receive_flag == 1){
            oled_show_num(0, 20, adcValue1, 4);
            oled_show_num(0, 40, adcValue2, 4);
            oled_show_num(60, 20, medAdcValue1, 4);
            oled_show_num(60, 40, medAdcValue2, 4);
            receive_flag = 0;
        }
        oled_update_screen();

        duty = (int32_t )((int )(adcValue1-medAdcValue1)*(float )4000/2040.0f);
        temp = (int16_t )((int )(adcValue2-medAdcValue2)*(float)4000/2040.0f);
        motor_set_duty(duty+temp, duty-temp);

        delay_ms(5);

        keyNum = get_key_num();
        if (keyNum == BACK)
            break;
    }
    motor_set_duty(0, 0);
    return BACK;
}
