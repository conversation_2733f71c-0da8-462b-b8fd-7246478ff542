//
// Created by faz<PERSON><PERSON> on 2024/7/24.
//

#include "imu.h"

static uint8_t buffer[11];
imu_data_t acceleration, gyroscope, angle;

void imu_init()
{
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART1_INT_IRQn);
    //使能串口中断
    NVIC_EnableIRQ(UART1_INT_IRQn);
}

static uint8_t get_verify_code()
{
    uint32_t sum = 0;
    for (uint8_t i = 0; i < 10; ++i) {
        sum += buffer[i];
    }
    return sum&0xff;
}

int32_t k = 0;
void imu_analysis()
{
    static float last_angle_z = 0, angle_z = 0;
    switch (buffer[1]) {
        case 0x51:
            acceleration.x = (float )((int16_t)(buffer[2]|(buffer[3]<<8)))/32768*16;
            acceleration.y = (float )((int16_t)(buffer[4]|(buffer[5]<<8)))/32768*16;
            acceleration.z = (float )((int16_t)(buffer[6]|(buffer[7]<<8)))/32768*16;
            break;
        case 0x52:
            gyroscope.x = (float )((int16_t)(buffer[2]|(buffer[3]<<8)))/32768*2000;
            gyroscope.y = (float )((int16_t)(buffer[4]|(buffer[5]<<8)))/32768*2000;
            gyroscope.z = (float )((int16_t)(buffer[6]|(buffer[7]<<8)))/32768*2000;
            break;
        case 0x53:
            angle.x = (float )((int16_t)(buffer[2]|(buffer[3]<<8)))/32768*180;
            angle.y = (float )((int16_t)(buffer[4]|(buffer[5]<<8)))/32768*180;
            angle_z = (float )((int16_t)(buffer[6]|(buffer[7]<<8)))/32768*180;
            break;
    }
    if (last_angle_z < -90 && angle_z > 90)
        k --;
    if (last_angle_z > 90 && angle_z < -90)
        k ++;

    last_angle_z = angle_z;
//    printf("k:%ld\n", k);
    angle.z = angle_z + (float )k*360;
}

uint8_t data;
#define ERROR   0
#define DOING   1
#define SUCCESS 2
static uint8_t n = 0, state = 0;
void UART1_IRQHandler(void)
{
//    printf("hhh\n");
    switch( DL_UART_getPendingInterrupt(UART1) )
    {
        case DL_UART_IIDX_RX:
            data = DL_UART_Main_receiveData(UART1);
//            printf("%d\n", data);
            if (state == ERROR){
                n = 0;
            }
            buffer[n] = (uint8_t)data;
            if (n == 0){
                if (buffer[0] == 0x55){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            } else if (n == 10){
                if (buffer[10] == get_verify_code()){
                    state = SUCCESS;
                } else{
                    state = ERROR;
                }
            }
            n += 1;
            if (state == SUCCESS){
//                printf("ok\n");
                imu_analysis();
                state = ERROR;
            }
            break;
        default://其他的串口中断
            break;
    }
}
