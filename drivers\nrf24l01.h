//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/25.
//

#ifndef NRF24L01_H
#define NRF24L01_H

#include "common_inc.h"

#define NRF24L01_SCK_PORT       GPIOA
#define NRF24L01_SCK_PIN        DL_GPIO_PIN_17

#define NRF24L01_MOSI_PORT      GPIOA
#define NRF24L01_MOSI_PIN       DL_GPIO_PIN_18

#define NRF24L01_MISO_PORT      GPIOA
#define NRF24L01_MISO_PIN       DL_GPIO_PIN_16

#define NRF24L01_CSN_PORT       GPIOA
#define NRF24L01_CSN_PIN        DL_GPIO_PIN_27

#define NRF24L01_CE_PORT        GPIOA
#define NRF24L01_CE_PIN         DL_GPIO_PIN_15

#define NRF24L01_IRQ_PORT       GPIOA
#define NRF24L01_IRQ_PIN        DL_GPIO_PIN_12

#define NRF24L01_SCK_OUT_H()        DL_GPIO_setPins(NRF24L01_SCK_PORT, NRF24L01_SCK_PIN)
#define NRF24L01_SCK_OUT_L()        DL_GPIO_clearPins(NRF24L01_SCK_PORT, NRF24L01_SCK_PIN)
#define NRF24L01_MOSI_OUT_H()       DL_GPIO_setPins(NRF24L01_MOSI_PORT, NRF24L01_MOSI_PIN)
#define NRF24L01_MOSI_OUT_L()       DL_GPIO_clearPins(NRF24L01_MOSI_PORT, NRF24L01_MOSI_PIN)
#define NRF24L01_CSN_OUT_H()        DL_GPIO_setPins(NRF24L01_CSN_PORT, NRF24L01_CSN_PIN)
#define NRF24L01_CSN_OUT_L()        DL_GPIO_clearPins(NRF24L01_CSN_PORT, NRF24L01_CSN_PIN)
#define NRF24L01_CE_OUT_H()         DL_GPIO_setPins(NRF24L01_CE_PORT, NRF24L01_CE_PIN)
#define NRF24L01_CE_OUT_L()         DL_GPIO_clearPins(NRF24L01_CE_PORT, NRF24L01_CE_PIN)

#define NRF24L01_MISO_IN()          DL_GPIO_readPins(NRF24L01_MISO_PORT, NRF24L01_MISO_PIN)
#define NRF24L01_IRQ_IN()           DL_GPIO_readPins(NRF24L01_IRQ_PORT, NRF24L01_IRQ_PIN)

//NRF24L01�Ĵ�����������
#define NRF24L01_READ_REG        0x00  //�����üĴ���,��5λΪ�Ĵ�����ַ
#define NRF24L01_WRITE_REG       0x20  //д���üĴ���,��5λΪ�Ĵ�����ַ
#define NRF24L01_RD_RX_PLOAD     0x61  //��RX��Ч����,1~32�ֽ�
#define NRF24L01_WR_TX_PLOAD     0xA0  //дTX��Ч����,1~32�ֽ�
#define NRF24L01_FLUSH_TX        0xE1  //���TX FIFO�Ĵ���.����ģʽ����
#define NRF24L01_FLUSH_RX        0xE2  //���RX FIFO�Ĵ���.����ģʽ����
#define NRF24L01_REUSE_TX_PL     0xE3  //����ʹ����һ������,CEΪ��,���ݰ������Ϸ���.
#define NRF24L01_NOP             0xFF  //�ղ���,����������״̬�Ĵ���
//SPI(NRF24L01)�Ĵ�����ַ
#define NRF24L01_CONFIG          0x00  //���üĴ�����ַ;bit0:1����ģʽ,0����ģʽ;bit1:��ѡ��;bit2:CRCģʽ;bit3:CRCʹ��;
//bit4:�ж�MAX_RT(�ﵽ����ط������ж�)ʹ��;bit5:�ж�TX_DSʹ��;bit6:�ж�RX_DRʹ��
#define NRF24L01_EN_AA           0x01  //ʹ���Զ�Ӧ����  bit0~5,��Ӧͨ��0~5
#define NRF24L01_EN_RXADDR       0x02  //���յ�ַ����,bit0~5,��Ӧͨ��0~5
#define NRF24L01_SETUP_AW        0x03  //���õ�ַ����(��������ͨ��):bit1,0:00,3�ֽ�;01,4�ֽ�;02,5�ֽ�;
#define NRF24L01_SETUP_RETR      0x04  //�����Զ��ط�;bit3:0,�Զ��ط�������;bit7:4,�Զ��ط���ʱ 250*x+86us
#define NRF24L01_RF_CH           0x05  //RFͨ��,bit6:0,����ͨ��Ƶ��;
#define NRF24L01_RF_SETUP        0x06  //RF�Ĵ���;bit3:��������(0:1Mbps,1:2Mbps);bit2:1,���书��;bit0:�������Ŵ�������
#define NRF24L01_STATUS          0x07  //״̬�Ĵ���;bit0:TX FIFO����־;bit3:1,��������ͨ����(���:6);bit4,�ﵽ�����ط�
//bit5:���ݷ�������ж�;bit6:���������ж�;
#define NRF24L01_MAX_TX  	0x10  //�ﵽ����ʹ����ж�
#define NRF24L01_TX_OK   	0x20  //TX��������ж�
#define NRF24L01_RX_OK   	0x40  //���յ������ж�

#define NRF24L01_OBSERVE_TX      0x08  //���ͼ��Ĵ���,bit7:4,���ݰ���ʧ������;bit3:0,�ط�������
#define NRF24L01_CD              0x09  //�ز����Ĵ���,bit0,�ز����;
#define NRF24L01_RX_ADDR_P0      0x0A  //����ͨ��0���յ�ַ,��󳤶�5���ֽ�,���ֽ���ǰ
#define NRF24L01_RX_ADDR_P1      0x0B  //����ͨ��1���յ�ַ,��󳤶�5���ֽ�,���ֽ���ǰ
#define NRF24L01_RX_ADDR_P2      0x0C  //����ͨ��2���յ�ַ,����ֽڿ�����,���ֽ�,����ͬRX_ADDR_P1[39:8]���;
#define NRF24L01_RX_ADDR_P3      0x0D  //����ͨ��3���յ�ַ,����ֽڿ�����,���ֽ�,����ͬRX_ADDR_P1[39:8]���;
#define NRF24L01_RX_ADDR_P4      0x0E  //����ͨ��4���յ�ַ,����ֽڿ�����,���ֽ�,����ͬRX_ADDR_P1[39:8]���;
#define NRF24L01_RX_ADDR_P5      0x0F  //����ͨ��5���յ�ַ,����ֽڿ�����,���ֽ�,����ͬRX_ADDR_P1[39:8]���;
#define NRF24L01_TX_ADDR         0x10  //���͵�ַ(���ֽ���ǰ),ShockBurstTMģʽ��,RX_ADDR_P0��˵�ַ���
#define NRF24L01_RX_PW_P0        0x11  //��������ͨ��0��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_RX_PW_P1        0x12  //��������ͨ��1��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_RX_PW_P2        0x13  //��������ͨ��2��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_RX_PW_P3        0x14  //��������ͨ��3��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_RX_PW_P4        0x15  //��������ͨ��4��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_RX_PW_P5        0x16  //��������ͨ��5��Ч���ݿ���(1~32�ֽ�),����Ϊ0��Ƿ�
#define NRF24L01_FIFO_STATUS     0x17  //FIFO״̬�Ĵ���;bit0,RX FIFO�Ĵ����ձ�־;bit1,RX FIFO����־;bit2,3,����
//bit4,TX FIFO�ձ�־;bit5,TX FIFO����־;bit6,1,ѭ��������һ���ݰ�.0,��ѭ��;
#define SI24R1_DYNPD             0x1c
#define SI24R1_FEATURE           0x1d

#define NRF24L01_TX_ADR_WIDTH    5   //5�ֽڵĵ�ַ����
#define NRF24L01_RX_ADR_WIDTH    5   //5�ֽڵĵ�ַ����
#define NRF24L01_TX_PLOAD_WIDTH  32  //20�ֽڵ��û����ݿ���
#define NRF24L01_RX_PLOAD_WIDTH  32  //20�ֽڵ��û����ݿ���
#define NRF24L01_TX_MODE         0
#define NRF24L01_RX_MODE         1

extern uint8_t receive_buffer[32];
extern uint8_t receive_flag;
extern uint8_t transmit_buffer[32];
extern uint8_t transmit_flag;

void nrf24l01_init();
uint8_t nrf24l01_check();
uint8_t nrf24l01_send_buffer(uint8_t * buffer);
uint8_t nrf24l01_receive_buffer(uint8_t * buffer);
void nrf24l01_set_tx_mode();
void nrf24l01_set_rx_mode();
void nrf24l01_receive_callback();
void nrf24l01_transmit_callback();
uint8_t nrf24l01_send_data(const uint8_t * data, uint8_t len);
void protocol_send_data(const uint8_t * buf, uint8_t len);

#endif //NRF24L01_H
