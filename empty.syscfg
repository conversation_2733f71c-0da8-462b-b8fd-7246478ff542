/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const PWM           = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1          = PWM.addInstance();
const PWM2          = PWM.addInstance();
const SPI           = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1          = SPI.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK       = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER         = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1        = TIMER.addInstance();
const TIMER2        = TIMER.addInstance();
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const UART2         = UART.addInstance();
const UART3         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.port                                = "PORTA";
GPIO1.$name                               = "GPIOA";
GPIO1.associatedPins.create(10);
GPIO1.associatedPins[0].$name             = "PIN_0";
GPIO1.associatedPins[0].assignedPin       = "0";
GPIO1.associatedPins[0].pin.$assign       = "PA0";
GPIO1.associatedPins[1].$name             = "PIN_1";
GPIO1.associatedPins[1].assignedPin       = "1";
GPIO1.associatedPins[1].pin.$assign       = "PA1";
GPIO1.associatedPins[2].assignedPin       = "25";
GPIO1.associatedPins[2].$name             = "PIN_25";
GPIO1.associatedPins[2].direction         = "INPUT";
GPIO1.associatedPins[2].interruptEn       = true;
GPIO1.associatedPins[2].polarity          = "RISE_FALL";
GPIO1.associatedPins[2].interruptPriority = "0";
GPIO1.associatedPins[3].$name             = "PIN_26";
GPIO1.associatedPins[3].assignedPin       = "26";
GPIO1.associatedPins[3].direction         = "INPUT";
GPIO1.associatedPins[3].interruptEn       = true;
GPIO1.associatedPins[3].polarity          = "RISE_FALL";
GPIO1.associatedPins[3].interruptPriority = "0";
GPIO1.associatedPins[4].$name             = "PIN_15";
GPIO1.associatedPins[4].assignedPin       = "15";
GPIO1.associatedPins[5].$name             = "PIN_27";
GPIO1.associatedPins[5].assignedPin       = "27";
GPIO1.associatedPins[6].$name             = "PIN_17";
GPIO1.associatedPins[6].assignedPin       = "17";
GPIO1.associatedPins[7].$name             = "PIN_18_";
GPIO1.associatedPins[7].assignedPin       = "18";
GPIO1.associatedPins[8].$name             = "PIN_16";
GPIO1.associatedPins[8].assignedPin       = "16";
GPIO1.associatedPins[8].direction         = "INPUT";
GPIO1.associatedPins[9].$name             = "PIN_12";
GPIO1.associatedPins[9].direction         = "INPUT";
GPIO1.associatedPins[9].interruptEn       = true;
GPIO1.associatedPins[9].polarity          = "FALL";
GPIO1.associatedPins[9].interruptPriority = "0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                               = "GPIOB";
GPIO2.port                                = "PORTB";
GPIO2.associatedPins.create(7);
GPIO2.associatedPins[0].$name             = "PIN_24";
GPIO2.associatedPins[0].direction         = "INPUT";
GPIO2.associatedPins[0].assignedPin       = "24";
GPIO2.associatedPins[0].internalResistor  = "PULL_UP";
GPIO2.associatedPins[0].pin.$assign       = "PB24";
GPIO2.associatedPins[1].$name             = "PIN_20";
GPIO2.associatedPins[1].direction         = "INPUT";
GPIO2.associatedPins[1].internalResistor  = "PULL_UP";
GPIO2.associatedPins[1].assignedPin       = "20";
GPIO2.associatedPins[2].$name             = "PIN_19";
GPIO2.associatedPins[2].internalResistor  = "PULL_UP";
GPIO2.associatedPins[2].assignedPin       = "19";
GPIO2.associatedPins[2].direction         = "INPUT";
GPIO2.associatedPins[3].$name             = "PIN_18";
GPIO2.associatedPins[3].direction         = "INPUT";
GPIO2.associatedPins[3].internalResistor  = "PULL_UP";
GPIO2.associatedPins[3].assignedPin       = "18";
GPIO2.associatedPins[4].$name             = "PIN_8";
GPIO2.associatedPins[4].direction         = "INPUT";
GPIO2.associatedPins[4].interruptEn       = true;
GPIO2.associatedPins[4].assignedPin       = "8";
GPIO2.associatedPins[4].polarity          = "RISE_FALL";
GPIO2.associatedPins[4].interruptPriority = "0";
GPIO2.associatedPins[5].$name             = "PIN_9";
GPIO2.associatedPins[5].direction         = "INPUT";
GPIO2.associatedPins[5].assignedPin       = "9";
GPIO2.associatedPins[5].interruptEn       = true;
GPIO2.associatedPins[5].polarity          = "RISE_FALL";
GPIO2.associatedPins[5].interruptPriority = "0";
GPIO2.associatedPins[6].$name             = "PIN_17_";
GPIO2.associatedPins[6].assignedPin       = "17";
GPIO2.associatedPins[6].pin.$assign       = "PB17";

GPIO3.$name                          = "TFT_PORT";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name        = "TFT_DC_PIN";
GPIO3.associatedPins[0].assignedPort = "PORTB";
GPIO3.associatedPins[0].assignedPin  = "10";
GPIO3.associatedPins[1].$name        = "TFT_RES_PIN";
GPIO3.associatedPins[1].assignedPort = "PORTB";
GPIO3.associatedPins[1].assignedPin  = "11";
GPIO3.associatedPins[2].$name        = "TFT_CS_PIN";
GPIO3.associatedPins[2].assignedPort = "PORTB";
GPIO3.associatedPins[2].assignedPin  = "12";
GPIO3.associatedPins[2].initialValue = "SET";
GPIO3.associatedPins[3].$name        = "TFT_BL_PIN";
GPIO3.associatedPins[3].initialValue = "SET";
GPIO3.associatedPins[3].assignedPort = "PORTB";
GPIO3.associatedPins[3].assignedPin  = "7";

PWM1.timerStartTimer                    = true;
PWM1.$name                              = "PWM_6";
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.timerCount                         = 6400;
PWM1.clockDivider                       = 5;
PWM1.peripheral.$assign                 = "TIMG6";
PWM1.peripheral.ccp0Pin.$assign         = "PB2";
PWM1.peripheral.ccp1Pin.$assign         = "PA22";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

PWM2.$name                              = "PWM_7";
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.clockDivider                       = 5;
PWM2.timerCount                         = 6400;
PWM2.timerStartTimer                    = true;
PWM2.peripheral.$assign                 = "TIMG7";
PWM2.peripheral.ccp0Pin.$assign         = "PA28";
PWM2.peripheral.ccp1Pin.$assign         = "PA24";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

SPI1.$name                              = "SPI_1";
SPI1.frameFormat                        = "MOTO3";
SPI1.sclkPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.sclkPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
SPI1.mosiPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion  = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";
SPI1.misoPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.misoPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.misoPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";
SPI1.peripheral.$assign                 = "SPI1";
SPI1.peripheral.sclkPin.$assign         = "PB16";
SPI1.peripheral.mosiPin.$assign         = "PB15";
SPI1.peripheral.misoPin.$assign         = "PB14";

SYSCTL.clockTreeEn = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.period            = 32000;
SYSTICK.interruptPriority = "3";

TIMER1.timerClkDiv        = 8;
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerClkPrescale   = 40;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.$name              = "TIMER_8";
TIMER1.interruptPriority  = "1";
TIMER1.timerPeriod        = "10 ms";
TIMER1.peripheral.$assign = "TIMG8";

TIMER2.$name              = "TIMER_12";
TIMER2.timerClkDiv        = 8;
TIMER2.timerStartTimer    = true;
TIMER2.interrupts         = ["ZERO"];
TIMER2.timerMode          = "PERIODIC";
TIMER2.interruptPriority  = "2";
TIMER2.timerPeriod        = "15 ms";
TIMER2.peripheral.$assign = "TIMG12";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.uartClkSrc               = "MFCLK";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_1";
UART2.enabledInterrupts        = ["RX"];
UART2.interruptPriority        = "0";
UART2.peripheral.$assign       = "UART1";
UART2.peripheral.rxPin.$assign = "PA9";
UART2.peripheral.txPin.$assign = "PA8";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric7";

UART3.$name                            = "UART_3";
UART3.enabledInterrupts                = ["RX"];
UART3.peripheral.$assign               = "UART3";
UART3.peripheral.rxPin.$assign         = "PA13";
UART3.peripheral.txPin.$assign         = "PA14";
UART3.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART3.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
UART3.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

ProjectConfig.deviceSpin = "MSPM0G3507";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[2].pin.$suggestSolution = "PA25";
GPIO1.associatedPins[3].pin.$suggestSolution = "PA26";
GPIO1.associatedPins[4].pin.$suggestSolution = "PA15";
GPIO1.associatedPins[5].pin.$suggestSolution = "PA27";
GPIO1.associatedPins[6].pin.$suggestSolution = "PA17";
GPIO1.associatedPins[7].pin.$suggestSolution = "PA18";
GPIO1.associatedPins[8].pin.$suggestSolution = "PA16";
GPIO1.associatedPins[9].pin.$suggestSolution = "PA12";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[1].pin.$suggestSolution = "PB20";
GPIO2.associatedPins[2].pin.$suggestSolution = "PB19";
GPIO2.associatedPins[3].pin.$suggestSolution = "PB18";
GPIO2.associatedPins[4].pin.$suggestSolution = "PB8";
GPIO2.associatedPins[5].pin.$suggestSolution = "PB9";
GPIO3.associatedPins[0].pin.$suggestSolution = "PB10";
GPIO3.associatedPins[1].pin.$suggestSolution = "PB11";
GPIO3.associatedPins[2].pin.$suggestSolution = "PB12";
GPIO3.associatedPins[3].pin.$suggestSolution = "PB7";
