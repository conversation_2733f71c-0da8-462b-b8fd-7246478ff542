//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/25.
//

#include "openmv.h"

static uint8_t rx_buffer[8];
static uint8_t tx_buffer[8];

openmv_data_t openmvData, openmvCommand;

void openmv_init()
{
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART3_INT_IRQn);
    //使能串口中断
    NVIC_EnableIRQ(UART3_INT_IRQn);
}

void openmv_analysis()
{
//	printf("OK\n");
    openmvData.num1 = rx_buffer[2];
    openmvData.num2 = rx_buffer[3];
    openmvData.num3 = rx_buffer[4];
    openmvData.num4 = rx_buffer[5];
}

static uint8_t get_verify_code(uint8_t *buffer)
{
    uint32_t sum = 0;
    for (int i = 0; i < 6; ++i) {
        sum += buffer[i];
    }

    return sum&0xff;
}

void send_openmv_command()
{
    tx_buffer[0] = 0x5a;
    tx_buffer[2] = openmvCommand.num1;
    tx_buffer[3] = openmvCommand.num2;
    tx_buffer[4] = openmvCommand.num3;
    tx_buffer[5] = openmvCommand.num4;
    tx_buffer[6] = get_verify_code(tx_buffer);
    tx_buffer[7] = 0xa5;

    for(uint8_t i = 0; i < 8; i++){
        //当串口0忙的时候等待，不忙的时候再发送传进来的字符
        while(DL_UART_isBusy(UART3) == true);
        //发送单个字符
        DL_UART_Main_transmitData(UART3, tx_buffer[i]);
    }
}

#define ERROR   0
#define DOING   1
#define SUCCESS 2
static uint8_t data;
static uint8_t n= 0, state = 0;
void UART3_IRQHandler(void)
{
    switch( DL_UART_getPendingInterrupt(UART3) )
    {
        case DL_UART_IIDX_RX:
            data = DL_UART_Main_receiveData(UART3);
//            debug_send_char(data);
            if (state == ERROR){
                n = 0;
            }

            rx_buffer[n] = data;
            if (n == 0){
                if (rx_buffer[0] == 0x5a){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            }
            else if (n == 6){
                if (rx_buffer[6] == get_verify_code(rx_buffer)){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            } else if (n == 7){
                if (rx_buffer[7] == 0xa5){
                    state = SUCCESS;
                } else{
                    state = ERROR;
                }
            }
            n += 1;
            if (state == SUCCESS){
                openmv_analysis();
                state = ERROR;
            }
            break;
        default://其他的串口中断
            break;
    }
}
