//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/22.
//

#ifndef COMMON_INC_H
#define COMMON_INC_H

#include <stdio.h>

#include "ti_msp_dl_config.h"

#include "delay.h"
#include "debug.h"
#include "vofa.h"
#include "menu.h"
#include "task.h"
#include "pid.h"
#include "protocol.h"

#include "key.h"
#include "motor.h"
#include "encoder.h"
#include "timer.h"
#include "openmv.h"
#include "flash.h"
#include "tft180.h"

extern int16_t target_speed;

void timerA_callback();
void timerB_callback();

#endif //COMMON_INC_H
