//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/25.
//

#include "nrf24l01.h"

static void nrf24l01_spi_delay()
{
    __NOP();
    __NOP();
    __NOP();
    __NOP();
}

uint8_t nrf24l01_spi_read_write(uint8_t data)
{
    uint8_t result = 0;
    for (uint8_t i = 0; i < 8; ++i) {

        if (0x80 & data){
            NRF24L01_MOSI_OUT_H();
        }
        else{
            NRF24L01_MOSI_OUT_L();
        }
        nrf24l01_spi_delay();
        NRF24L01_SCK_OUT_H();
        data <<= 1;
        result <<= 1;
        if (NRF24L01_MISO_IN())
            result |= 0x01;
        nrf24l01_spi_delay();
        NRF24L01_SCK_OUT_L();
    }
    return result;
}

static uint8_t nrf24l01_write_reg(uint8_t reg, uint8_t data)
{
    uint8_t status;
    NRF24L01_CSN_OUT_L();
    status = nrf24l01_spi_read_write(reg);
    nrf24l01_spi_read_write(data);
    NRF24L01_CSN_OUT_H();
    return status;
}

static uint8_t nrf24l01_read_reg(uint8_t reg)
{
    uint8_t status;
    NRF24L01_CSN_OUT_L();
    nrf24l01_spi_read_write(reg);
    status = nrf24l01_spi_read_write(0);
    NRF24L01_CSN_OUT_H();
    return status;
}

static uint8_t nrf24l01_read_buf(uint8_t reg, uint8_t * buffer, uint8_t len)
{
    uint8_t  status;
    NRF24L01_CSN_OUT_L();
    status = nrf24l01_spi_read_write(reg);
    for (int i = 0; i < len; ++i) {
        buffer[i] = nrf24l01_spi_read_write(0);
    }
    NRF24L01_CSN_OUT_H();
    return status;
}

static uint8_t nrf24l01_write_buf(uint8_t reg, uint8_t * buffer, uint8_t len)
{
    uint8_t status;
    NRF24L01_CSN_OUT_L();
    status = nrf24l01_spi_read_write(reg);
    for (int i = 0; i < len; ++i) {
        nrf24l01_spi_read_write(buffer[i]);
    }
    NRF24L01_CSN_OUT_H();
    return status;
}

uint8_t nrf24l01_check()
{
    NRF24L01_CSN_OUT_H();
    NRF24L01_CE_OUT_L();
    NRF24L01_SCK_OUT_L();

    uint8_t buffer[] = {0xa5, 0xa5, 0xa5, 0xa5, 0xa5};
    nrf24l01_write_buf(NRF24L01_WRITE_REG+NRF24L01_TX_ADDR, buffer, 5);
    memset(buffer, 0, 5);
    nrf24l01_read_buf(NRF24L01_TX_ADDR, buffer, 5);
    int i = 0;
    for (; i < 5; ++i) {
        if (buffer[i] != 0xa5)
            break;
    }
    if (i != 5)
        return 1;
    return 0;
}

const uint8_t ADDRESS[NRF24L01_TX_ADR_WIDTH]={0x34,0x43,0x10,0x10,0x01}; //���͵�ַ
void nrf24l01_init()
{
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_SETUP_AW, NRF24L01_RX_ADR_WIDTH);
    nrf24l01_write_buf(NRF24L01_WRITE_REG+NRF24L01_TX_ADDR, (uint8_t *)ADDRESS, NRF24L01_TX_ADR_WIDTH);//дTX�ڵ��ַ
    nrf24l01_write_buf(NRF24L01_WRITE_REG+NRF24L01_RX_ADDR_P0, (uint8_t *)ADDRESS, NRF24L01_RX_ADR_WIDTH); //����TX�ڵ��ַ,��ҪΪ��ʹ��ACK
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_EN_AA,0x01);     //ʹ��ͨ��0���Զ�Ӧ��
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_EN_RXADDR,0x01); //ʹ��ͨ��0�Ľ��յ�ַ
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_SETUP_RETR,0x1a);//�����Զ��ط����ʱ��:500us + 86us;����Զ��ط�����:10��
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_RF_CH,40);       //����RFͨ��Ϊ40
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_RX_PW_P0,NRF24L01_RX_PLOAD_WIDTH);//ѡ��ͨ��0����Ч���ݿ���
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_RF_SETUP,0x0f);//����TX�������,0db����,2Mbps,���������濪��
}

void nrf24l01_set_rx_mode()
{
    NRF24L01_CE_OUT_L();
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_CONFIG, 0x0f);//���û�������ģʽ�Ĳ���;PWR_UP,EN_CRC,16BIT_CRC,����ģʽ
    nrf24l01_write_reg(NRF24L01_FLUSH_RX,0xff);//���RX FIFO�Ĵ���
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_STATUS, 0xff);
    NRF24L01_CE_OUT_H();
}

void nrf24l01_set_tx_mode()
{
    NRF24L01_CE_OUT_L();
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_CONFIG,0x0e);    //���û�������ģʽ�Ĳ���;PWR_UP,EN_CRC,16BIT_CRC,����ģʽ,���������ж�
}

uint8_t receive_buffer[32];
uint8_t receive_flag = 0;
uint8_t irq = 0;
void nrf24l01_receive_callback()
{
    uint8_t status;
    irq = 0;
    uint32_t gpioA = DL_GPIO_getEnabledInterruptStatus(GPIOA, NRF24L01_IRQ_PIN);
    if ((gpioA & NRF24L01_IRQ_PIN) == NRF24L01_IRQ_PIN){
        DL_GPIO_clearInterruptStatus(GPIOA, NRF24L01_IRQ_PIN);
        NRF24L01_CE_OUT_L();
        status = nrf24l01_read_reg(NRF24L01_STATUS);
        nrf24l01_write_reg(NRF24L01_WRITE_REG + NRF24L01_STATUS, status);
        if (status & NRF24L01_RX_OK) {
            nrf24l01_read_buf(NRF24L01_RD_RX_PLOAD, receive_buffer, NRF24L01_RX_PLOAD_WIDTH);
            nrf24l01_write_reg(NRF24L01_FLUSH_RX, 0xff);
            protocol_analysis();
            receive_flag = 1;
        }
        nrf24l01_write_reg(NRF24L01_FLUSH_RX, 0xff);
        NRF24L01_CE_OUT_H();
    }
}

uint8_t transmit_buffer[32];
uint8_t transmit_flag = 0; // 1 means normal 0 means error
void nrf24l01_transmit_callback()
{
    NRF24L01_CE_OUT_L();
    nrf24l01_write_buf(NRF24L01_WR_TX_PLOAD, transmit_buffer, NRF24L01_TX_PLOAD_WIDTH);
    NRF24L01_CE_OUT_H();
    irq = 1;
    while (irq == 1){
        __NOP();
        __NOP();
        __NOP();
        __NOP();
    }
    uint8_t status = nrf24l01_read_reg(NRF24L01_STATUS);
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_STATUS, status);
    transmit_flag = 1;
    if (status & NRF24L01_MAX_TX){
        nrf24l01_write_reg(NRF24L01_FLUSH_TX, 0xff);
        transmit_flag = 0;
    }
}

uint8_t nrf24l01_send_buffer(uint8_t * buffer)
{
    uint8_t status;

    NRF24L01_CE_OUT_L();
    nrf24l01_write_buf(NRF24L01_WR_TX_PLOAD, buffer, NRF24L01_TX_PLOAD_WIDTH);
    NRF24L01_CE_OUT_H();
    while (NRF24L01_IRQ_IN() != 0);
    status = nrf24l01_read_reg(NRF24L01_STATUS);
    nrf24l01_write_reg(NRF24L01_WRITE_REG+NRF24L01_STATUS, status);
    if (status & NRF24L01_MAX_TX){
        nrf24l01_write_reg(NRF24L01_FLUSH_TX, 0xff);
        return NRF24L01_MAX_TX;
    }
    if (status & NRF24L01_TX_OK){
        return NRF24L01_TX_OK;
    }
    return 0xff;
}

uint8_t buffer[32];
uint8_t nrf24l01_send_data(const uint8_t * data, uint8_t len)
{
    receive_flag = 1;
    nrf24l01_set_tx_mode();
    if (len > 30)
        return 0;
    memset(buffer, 0, 32);
    buffer[0] = 'D';
    buffer[1] = len;
    for (uint8_t i = 0; i < 30; ++i) {
        if (i < len)
            buffer[i+2] = data[i];
        else
            buffer[i+2] = 0;
    }
    uint8_t state = nrf24l01_send_buffer(buffer);
    nrf24l01_set_rx_mode();
    receive_flag = 0;
    return state;
}

// non-block
void protocol_send_data(const uint8_t * buf, uint8_t len)
{
    static uint8_t temp_buffer[255];
    static uint8_t waitSendLen = 0, index = 0;
    // check send
    if (waitSendLen == 0){
        memset(temp_buffer, 0, len);
        waitSendLen = len;
        index = 0;
        if (len > 250)
            return;
        // copy buffer
        for (int i = 0; i < len; ++i) {
            temp_buffer[i] = buf[i];
        }
    }
    else{
        // send data
        // no loop
        // copy to transmitBuffer
        if (transmit_flag == 0){
//            memset(remote.transmitBuffer, 0, 32);
            transmit_buffer[0] = 'D';
            if (waitSendLen >= 30){
                transmit_buffer[1] = 30;
                for (int i = 0; i < 30; ++i) {
                    transmit_buffer[i+2] = temp_buffer[i+index];
                }
                waitSendLen -= 30;
                index += 30;
            }
            else{
                transmit_buffer[1] = waitSendLen;
                for (int i = 0; i < waitSendLen; ++i) {
                    transmit_buffer[i+2] = temp_buffer[i+index];
                }
                index += waitSendLen;
                waitSendLen = 0;
            }
            // start send
            transmit_flag = 1;
        }
    }
}
